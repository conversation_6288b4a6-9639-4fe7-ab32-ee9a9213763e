-- Simple Linden Awards Insert
-- Client ID: 155 (<PERSON>)
-- User ID: 18 (Assignee)

INSERT INTO awards (
    string_id,
    client_id,
    assignee_id,
    funder,
    grant_program_name,
    department,
    starts_on,
    ends_on,
    award_amount,
    award_balance,
    award_expended,
    status,
    source,
    category,
    "applicationRequired",
    "awardLetterRequired",
    "resolutionRequired",
    "initialAgreementRequired",
    "contractMaterialRequired",
    "reportRequired",
    match_amount,
    match_expended,
    match_balance,
    payments_requested,
    payments_received,
    is_approved,
    "isCompliance",
    enabled,
    "createdAt",
    "updatedAt"
) VALUES
('DEV-LIN001', 155, 18, 'NJ Department of Community Affairs', 'Neighborhood Preservation Program (NPP)', 'Engineering', '2023-09-01', '2024-06-30 23:59:59+00', 263000.00, 0.00, 263000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN002', 155, 18, 'NJ Department of Community Affairs', 'Neighborhood Preservation Program (NPP)', 'Engineering', '2024-09-01', '2025-06-30 23:59:59+00', 214000.00, 214000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN003', 155, 18, 'NJ Department of Environmental Protection', 'Stormwater Assistance Grants Program FY23', 'Engineering', '2023-07-01', '2026-01-01 23:59:59+00', 25000.00, 25000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN004', 155, 18, 'NJ Department of Transportation', 'Local Freight Impact Fund (LFIF) FY18', 'Engineering', NULL, NULL, 335000.00, 539.43, 334460.57, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN005', 155, 18, 'NJ Department of Transportation', 'Local Freight Impact Fund (LFIF) FY18', 'Engineering', '2018-08-27', NULL, 1250000.00, 515202.96, 734797.04, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN006', 155, 18, 'NJ Department of Transportation', 'Local Freight Impact Fund (LFIF) FY20', 'Engineering', '2020-07-22', '2022-07-22 23:59:59+00', 2302443.00, 1484180.00, 818263.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN007', 155, 18, 'NJ Department of Transportation', 'Municipal Aid Grant Program FY20', 'Engineering', '2019-11-01', '2021-11-01 23:59:59+00', 405000.00, 1200182.61, -795182.61, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN008', 155, 18, 'NJ Department of Transportation', 'Municipal Aid Grant Program FY22', 'Engineering', '2021-11-01', '2024-11-01 23:59:59+00', 440000.00, 1723687.74, -1283687.74, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN009', 155, 18, 'NJ Department of Transportation', 'Municipal Aid Grant Program FY23', 'Engineering', '2022-11-01', '2025-11-01 23:59:59+00', 379110.00, 94777.50, 284332.50, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN010', 155, 18, 'NJ Department of Transportation', 'Municipal Aid Grant Program FY24', 'Engineering', '2023-11-01', '2026-11-01 23:59:59+00', 485000.00, 485000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN011', 155, 18, 'Phillips 66', 'Phillips 66 Blue Cares Donation', 'Engineering', NULL, NULL, 15000.00, 15000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN012', 155, 18, 'Phillips 66', 'Phillips 66 Quiet Zone Donation', 'Engineering', NULL, NULL, 30000.00, 30000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN013', 155, 18, 'Union County', 'Community Development Block Grants (CDBG) FY23', 'Engineering', '2022-09-01', '2023-08-30 23:59:59+00', 310000.00, 4626.07, 305373.93, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN014', 155, 18, 'Union County', 'Community Development Block Grants (CDBG) Program FY22', 'Engineering', '2021-09-01', '2022-08-30 23:59:59+00', 370000.00, 0.00, 370000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN015', 155, 18, 'Union County', 'Community Development Block Grants (CDBG) Program FY24', 'Engineering', '2023-09-01', '2024-08-30 23:59:59+00', 310000.00, 310000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN016', 155, 18, 'Union County', 'Infrastructure and Municipal Aid Grant FY22', 'Engineering', '2022-01-01', '2022-12-31 23:59:59+00', 90000.00, 0.00, 90000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN017', 155, 18, 'Union County', 'Infrastructure and Municipal Aid Grant FY24', 'Engineering', '2024-01-01', '2024-12-31 23:59:59+00', 95000.00, 95000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN018', 155, 18, 'Enbridge Inc.', 'Fueling Futures Program', 'Fire', NULL, NULL, 8565.00, 77.50, 8487.50, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN019', 155, 18, 'NJ Department of Community Affairs', 'Legislative Grant FY24', 'Fire', NULL, NULL, 2000000.00, 1805000.00, 195000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN020', 155, 18, 'Phillips 66', 'Phillips 66 FD Donation', 'Fire', NULL, NULL, 20000.00, 22.25, 19977.75, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN021', 155, 18, 'City of Plainfield', 'HUD Lead Hazard Reduction Grants Program FY18', 'Health', '2018-11-15', '2022-11-15 23:59:59+00', 4000.00, 789.55, 3210.45, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN022', 155, 18, 'City of Plainfield', 'HUD Lead Hazard Reduction Grants Program FY23', 'Health', '2023-11-15', '2027-11-15 23:59:59+00', 12000.00, 2264.00, 9736.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN023', 155, 18, 'City of Plainfield', 'HUD Lead Hazard Reduction Grants Program FY24', 'Health', '2024-11-15', '2028-11-15 23:59:59+00', 30160.00, 185.00, 29975.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN024', 155, 18, 'NJ Association of County and City Health Officials', 'Enhancing Local Public Health Infrastructure Grants Program FY23', 'Health', '2023-07-01', '2024-06-30 23:59:59+00', 599677.00, 58835.22, 540841.78, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN025', 155, 18, 'NJ Department of Health', 'COVID-19 Vaccination Supplemental Funding Grants Program FY22/23', 'Health', '2022-07-01', '2023-06-30 23:59:59+00', 135000.00, 0.00, 135000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN026', 155, 18, 'NJ Department of Health', 'Strengthening Local Public Health Capacity Grants Program FY23', 'Health', '2023-07-01', '2024-06-30 23:59:59+00', 274735.00, 162.00, 274573.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN027', 155, 18, 'NJ Department of Health', 'Strengthening Local Public Health Capacity Grants Program FY24', 'Health', '2024-07-01', '2025-06-30 23:59:59+00', 406046.00, 116694.42, 289351.58, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN028', 155, 18, 'NJ Department of Health', 'Strengthening Local Public Health Capacity Grants Program FY25', 'Health', '2025-07-01', '2026-06-30 23:59:59+00', 74664.00, 35801.07, 38862.93, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN029', 155, 18, 'National Opioid Settlement', 'Allergan Settlement', 'Mayor''s Office', '2024-04-30', NULL, 19832.37, 0.00, 19832.37, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN030', 155, 18, 'National Opioid Settlement', 'CVS Settlement', 'Mayor''s Office', '2024-04-30', NULL, 19106.35, 0.00, 19106.35, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN031', 155, 18, 'National Opioid Settlement', 'J&J Settlement', 'Mayor''s Office', '2024-12-30', NULL, 31270.55, 6027.74, 25242.81, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN032', 155, 18, 'National Opioid Settlement', 'Janssen Settlement', 'Mayor''s Office', '2023-05-19', NULL, 17113.37, 3153.35, 13960.02, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN033', 155, 18, 'National Opioid Settlement', 'Janssen Settlement', 'Mayor''s Office', '2024-06-20', NULL, 27133.01, 0.00, 27133.01, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN034', 155, 18, 'National Opioid Settlement', 'McKinsey Settlement', 'Mayor''s Office', '2023-11-09', NULL, 6187.31, 0.00, 6187.31, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN035', 155, 18, 'National Opioid Settlement', 'McKinsey Settlement', 'Mayor''s Office', '2024-04-30', NULL, 12499.84, 0.00, 12499.84, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN036', 155, 18, 'National Opioid Settlement', 'Opioid Settlement', 'Mayor''s Office', '2024-04-30', NULL, 46877.00, 0.00, 46877.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN037', 155, 18, 'National Opioid Settlement', 'Opioid Settlement FY22', 'Mayor''s Office', '2022-08-04', NULL, 51275.67, 818.91, 50456.76, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN038', 155, 18, 'National Opioid Settlement', 'Teva Settlement', 'Mayor''s Office', '2024-04-30', NULL, 18578.98, 0.00, 18578.98, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN039', 155, 18, 'National Opioid Settlement', 'Walgreens Settlement', 'Mayor''s Office', '2024-04-30', NULL, 21814.03, 0.00, 21814.03, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN040', 155, 18, 'National Opioid Settlement', 'Walmart Settlement', 'Mayor''s Office', '2024-04-30', NULL, 86428.08, 0.00, 86428.08, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN041', 155, 18, 'National Opioid Settlement', 'Abatement Settlement', 'Mayor''s Office', '2023-05-19', NULL, 5259.21, 0.00, 5259.21, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN042', 155, 18, 'National Opioid Settlement', 'Distributor Settlement', 'Mayor''s Office', '2023-08-21', NULL, 24289.55, 0.00, 24289.55, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN043', 155, 18, 'National Opioid Settlement', 'Endo Public Settlement', 'Mayor''s Office', '2024-10-10', NULL, 10828.32, 0.00, 10828.32, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN044', 155, 18, 'City of Linden', 'Capital Funding Police Allocation', 'Police', NULL, NULL, 350000.00, 114267.10, 235732.90, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN045', 155, 18, 'City of Linden', 'Capital Funding Police Allocation', 'Police', NULL, NULL, 400000.00, 19171.14, 380828.86, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN046', 155, 18, 'NJ Department of Human Services', 'Alcohol Education, Rehabilitation and Enforcement Fund FY19', 'Police', NULL, NULL, 5947.73, 48.81, 5898.92, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN047', 155, 18, 'NJ Department of Human Services', 'Alcohol Education, Rehabilitation and Enforcement Fund FY20', 'Police', NULL, NULL, 2667.32, 1467.32, 1200.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN048', 155, 18, 'NJ Department of Law and Public Safety', 'Safe and Secure Communities Grants Program FY22', 'Police', NULL, NULL, 32400.00, 0.00, 32400.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN049', 155, 18, 'NJ Department of Law and Public Safety', 'Safe and Secure Communities Grants Program FY23', 'Police', NULL, NULL, 32400.00, 0.00, 32400.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN050', 155, 18, 'NJ Department of Law and Public Safety', 'Safe and Secure Communities Grants Program FY24', 'Police', NULL, NULL, 45150.00, 0.00, 45150.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN051', 155, 18, 'NJ Department of Law and Public Safety', 'Safe and Secure Communities Grants Program FY25', 'Police', NULL, NULL, 45150.00, 0.00, 45150.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN052', 155, 18, 'NJ Department of Transportation', 'Safe Corridor Program FY22', 'Police', NULL, NULL, 57217.00, 0.00, 57217.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN053', 155, 18, 'NJ Division of Criminal Justice', 'State Body Armor Replacement Fund Program FY20', 'Police', NULL, NULL, 8970.82, 97.62, 8873.20, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN054', 155, 18, 'NJ Division of Criminal Justice', 'State Body Armor Replacement Fund Program FY21', 'Police', NULL, NULL, 5656.46, 0.00, 5656.46, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN055', 155, 18, 'NJ Division of Criminal Justice', 'State Body Armor Replacement Fund Program FY22', 'Police', NULL, NULL, 8074.13, 0.00, 8074.13, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN056', 155, 18, 'NJ Division of Criminal Justice', 'State Body Armor Replacement Fund Program FY23', 'Police', NULL, NULL, 9650.30, 0.00, 9650.30, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN055', 155, 18, 'NJ Division of Highway Traffic Safety', 'Click It or Ticket Mobilization FY22', 'Police', '2022-05-23', '2022-06-05 23:59:59+00', 7000.00, 0.00, 7000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN056', 155, 18, 'NJ Division of Highway Traffic Safety', 'Click It or Ticket Mobilization FY23', 'Police', '2023-05-22', '2023-06-04 23:59:59+00', 7000.00, 0.00, 7000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN057', 155, 18, 'NJ Division of Highway Traffic Safety', 'Click It or Ticket Mobilization FY24', 'Police', '2024-05-20', '2024-06-02 23:59:59+00', 8750.00, 0.00, 8750.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN058', 155, 18, 'NJ Division of Highway Traffic Safety', 'Click It or Ticket Mobilization FY25', 'Police', '2025-05-19', '2025-06-01 23:59:59+00', 0.00, 0.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN059', 155, 18, 'NJ Division of Highway Traffic Safety', 'Distracted Driving Statewide Crackdown Grant', 'Police', '2023-04-01', '2023-04-30 23:59:59+00', 14000.00, 0.00, 14000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN060', 155, 18, 'NJ Division of Highway Traffic Safety', 'Distracted Driving Statewide Crackdown Grant FY22', 'Police', '2022-04-01', '2022-04-30 23:59:59+00', 14000.00, 0.00, 14000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN061', 155, 18, 'NJ Division of Highway Traffic Safety', 'Distracted Driving Statewide Crackdown Grant FY24', 'Police', '2024-04-01', '2024-04-30 23:59:59+00', 7000.00, 3220.00, 3780.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN062', 155, 18, 'NJ Division of Highway Traffic Safety', 'Drive Sober or Get Pulled Over Statewide Labor Day Crackdown FY22', 'Police', '2022-08-19', '2022-09-05 23:59:59+00', 10500.00, 0.00, 10500.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN063', 155, 18, 'NJ Division of Highway Traffic Safety', 'Drive Sober or Get Pulled Over Statewide Labor Day Crackdown FY23', 'Police', '2023-08-18', '2023-09-04 23:59:59+00', 8750.00, 0.00, 8750.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN064', 155, 18, 'NJ Division of Highway Traffic Safety', 'Drive Sober or Get Pulled Over Statewide Labor Day Crackdown FY24', 'Police', '2024-08-16', '2024-09-02 23:59:59+00', 7000.00, 0.00, 7000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN065', 155, 18, 'NJ Division of Highway Traffic Safety', 'Drive Sober or Get Pulled Over Statewide Labor Day Crackdown FY25', 'Police', NULL, NULL, 0.00, 0.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN066', 155, 18, 'NJ Division of Highway Traffic Safety', 'Drive Sober or Get Pulled Over Year End Holiday Crackdown FY23', 'Police', '2023-12-01', '2025-01-01 23:59:59+00', 12250.00, 0.00, 12250.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN067', 155, 18, 'NJ Division of Highway Traffic Safety', 'Drive Sober or Get Pulled Over Year End Holiday Crackdown FY24', 'Police', '2024-12-06', '2025-01-01 23:59:59+00', 0.00, 0.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN068', 155, 18, 'NJ Division of Highway Traffic Safety', 'Drunk Driving Enforcement Fund (DDEF) Grants Program', 'Police', NULL, NULL, 0.00, 0.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN069', 155, 18, 'NJ Division of Highway Traffic Safety', 'Drunk Driving Enforcement Fund (DDEF) Grants Program FY19', 'Police', '2018-07-01', '2019-06-30 23:59:59+00', 18297.68, 4.52, 18293.16, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN070', 155, 18, 'NJ Division of Highway Traffic Safety', 'Drunk Driving Enforcement Fund (DDEF) Grants Program FY24', 'Police', '2023-07-01', '2024-06-30 23:59:59+00', 46785.94, 29800.94, 16985.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN071', 155, 18, 'Phillips 66', 'Phillips 66 911 Comms Donation', 'Police', NULL, NULL, 14000.00, 4097.73, 9902.27, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN072', 155, 18, 'US Department of Justice', 'Byrne Discretionary Grant Program FY24', 'Police', NULL, NULL, 400000.00, 118400.00, 281600.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN073', 155, 18, 'US Department of Justice', 'Coronavirus Emergency Supplemental Funding (CESF) Program FY20', 'Police', '2020-01-20', '2022-01-31 23:59:59+00', 46938.00, 0.00, 46938.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN074', 155, 18, 'US Department of Justice', 'Edward Bryne Memorial Justice Assistance Grant FY21', 'Police', '2020-10-01', '2022-10-01 23:59:59+00', 17209.00, 0.00, 17209.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN075', 155, 18, 'US Department of Justice', 'Edward Byrne Memorial Justice Assistance Grant (JAG) FY18', 'Police', '2017-10-01', '2019-10-02 23:59:59+00', 24264.79, 10256.29, 14008.50, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN076', 155, 18, 'US Department of Justice', 'Edward Byrne Memorial Justice Assistance Grant (JAG) FY19', 'Police', '2018-10-01', '2020-10-01 23:59:59+00', 22806.16, 22806.16, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN077', 155, 18, 'US Department of Justice', 'Edward Byrne Memorial Justice Assistance Grant (JAG) FY20', 'Police', '2019-10-01', '2021-10-01 23:59:59+00', 20222.80, 20222.80, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN078', 155, 18, 'US Department of Justice', 'Edward Byrne Memorial Justice Assistance Grant (JAG) FY22', 'Police', '2021-10-01', '2023-10-01 23:59:59+00', 19313.00, 0.00, 19313.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN079', 155, 18, 'US Department of Justice', 'Edward Byrne Memorial Justice Assistance Grant (JAG) FY23', 'Police', '2022-10-01', '2024-10-01 23:59:59+00', 21794.00, 14040.43, 7753.57, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN080', 155, 18, 'US Department of Justice', 'Edward Byrne Memorial Justice Assistance Grant (JAG) FY24', 'Police', '2023-10-01', '2025-10-01 23:59:59+00', 19313.00, 0.00, 19313.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN081', 155, 18, 'Linden Board of Education', 'Green Acres Reforestation Grant', 'Public Works', NULL, NULL, 606644.29, 606644.29, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN082', 155, 18, 'NJ Department of Community Affairs', 'Legislative Grant FY23', 'Public Works', '2022-07-01', '2023-06-30 23:59:59+00', 1500000.00, 0.00, 1500000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN083', 155, 18, 'NJ Department of Environmental Protection', 'Clean Communities Grant FY21', 'Public Works', '2021-01-01', '2022-06-30 23:59:59+00', 73352.94, 33.17, 73319.77, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN084', 155, 18, 'NJ Department of Environmental Protection', 'Clean Communities Grant FY22', 'Public Works', '2022-01-01', '2023-06-30 23:59:59+00', 74921.87, 11515.09, 63406.78, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN085', 155, 18, 'NJ Department of Environmental Protection', 'Clean Communities Grant FY23', 'Public Works', '2023-01-01', '2024-06-30 23:59:59+00', 84034.31, 77220.72, 6813.59, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN086', 155, 18, 'NJ Department of Environmental Protection', 'Clean Communities Grant FY24', 'Public Works', '2024-01-01', '2025-06-30 23:59:59+00', 95448.66, 95448.66, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN087', 155, 18, 'NJ Department of Environmental Protection', 'Clean Communities Grant FY25', 'Public Works', '2025-01-01', '2026-06-30 23:59:59+00', 0.00, 0.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN088', 155, 18, 'NJ Department of Environmental Protection', 'Municipal Tonnage Grant Program FY19', 'Public Works', '2020-08-30', '2021-10-01 23:59:59+00', 76001.04, 0.00, 76001.04, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN089', 155, 18, 'NJ Department of Environmental Protection', 'Municipal Tonnage Grant Program FY20', 'Public Works', '2021-08-30', '2022-10-01 23:59:59+00', 79670.82, 0.00, 79670.82, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN090', 155, 18, 'NJ Department of Environmental Protection', 'Municipal Tonnage Grant Program FY21', 'Public Works', '2022-08-30', '2023-10-01 23:59:59+00', 89130.02, 0.00, 79670.82, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN091', 155, 18, 'Phillips 66', 'Phillips 66 Tree Planting Donation', 'Public Works', NULL, NULL, 33000.00, 0.00, 33000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN092', 155, 18, 'Union County', 'Greening Union County Grant Program FY22', 'Public Works', '2022-11-10', '2023-12-31 23:59:59+00', 8000.00, 0.00, 8000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN093', 155, 18, 'Union County', 'Greening Union County Grant Program FY22', 'Public Works', '2022-11-10', '2023-12-31 23:59:59+00', 8000.00, 8000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN094', 155, 18, 'Union County', 'Greening Union County Grant Program FY23', 'Public Works', '2023-11-20', '2024-12-31 23:59:59+00', 8000.00, 8000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN095', 155, 18, 'Union County', 'Greening Union County Grant Program FY23', 'Public Works', '2023-11-20', '2024-12-31 23:59:59+00', 8000.00, 8000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN096', 155, 18, 'Union County', 'Greening Union County Grant Program FY25', 'Public Works', '2025-11-20', '2026-12-31 23:59:59+00', 0.00, 0.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN097', 155, 18, 'Comcast', 'Cable Franchise Fee Revenue', 'Recreation', NULL, NULL, 190117.16, 551.16, 189566.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN098', 155, 18, 'Comcast', 'Cable Franchise Fee Revenue FY09', 'Recreation', NULL, NULL, 97623.31, 275.58, 97347.73, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN099', 155, 18, 'Comcast', 'Cable Franchise Fee Revenue FY22', 'Recreation', NULL, NULL, 72377.04, 0.00, 72377.04, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN100', 155, 18, 'Comcast', 'Cable Franchise Fee Revenue FY23', 'Recreation', NULL, NULL, 68759.62, 1678.41, 67081.21, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN101', 155, 18, 'Comcast', 'Cable Franchise Fee Revenue FY24', 'Recreation', NULL, NULL, 61540.45, 26685.41, 34855.04, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN102', 155, 18, 'Comcast', 'Cable Franchise Fee Revenue FY25', 'Recreation', NULL, NULL, 57965.10, 47879.26, 10085.84, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN103', 155, 18, 'Comcast', 'Community Programs FY19', 'Recreation', NULL, NULL, 5000.00, 4.28, 4995.72, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN104', 155, 18, 'Comcast', 'Community Programs FY20', 'Recreation', NULL, NULL, 5000.00, 37.98, 4962.02, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN105', 155, 18, 'Comcast', 'Community Programs FY22', 'Recreation', NULL, NULL, 5000.00, 0.00, 5000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN106', 155, 18, 'Comcast', 'Community Programs FY23', 'Recreation', NULL, NULL, 5000.00, 5000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN107', 155, 18, 'Comcast', 'Community Programs FY24', 'Recreation', NULL, NULL, 5000.00, 5000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN108', 155, 18, 'Comcast', 'Community Programs FY25', 'Recreation', NULL, NULL, 0.00, 0.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN109', 155, 18, 'Comcast', 'PGE Grant FY18', 'Recreation', '2018-09-28', NULL, 100000.00, 411.25, 99588.75, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN110', 155, 18, 'NJ Department of Community Affairs', 'Legislative Grant FY25', 'Recreation', '2024-07-01', '2026-12-31 23:59:59+00', 2100000.00, 2100000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN111', 155, 18, 'NJ Department of Community Affairs', 'Local Recreation Improvement Grant (LRIG) Program FY23', 'Recreation', '2023-08-02', NULL, 87000.00, 87000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN112', 155, 18, 'NJ Department of Environmental Protection', 'Green Acres Park Development Grants Program FY20', 'Recreation', NULL, NULL, 500793.00, 1143.27, 499649.73, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN113', 155, 18, 'Phillips 66', 'Phillips 66 Milkosky Park Donation', 'Recreation', '2022-09-27', NULL, 63000.00, 51620.00, 11380.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN114', 155, 18, 'Union County', 'Kids Recreation Trust Fund FY04', 'Recreation', '2004-11-10', '2006-11-10 23:59:59+00', 55000.00, 55000.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN115', 155, 18, 'Union County', 'Kids Recreation Trust Fund FY20', 'Recreation', '2020-11-10', '2022-11-10 23:59:59+00', 200000.00, 0.00, 200000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN116', 155, 18, 'Union County', 'Kids Recreation Trust Fund FY21', 'Recreation', '2021-11-10', '2023-11-10 23:59:59+00', 70000.00, 3971.93, 66028.07, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN117', 155, 18, 'Union County', 'Kids Recreation Trust Fund FY22', 'Recreation', '2022-11-10', '2024-11-10 23:59:59+00', 50000.00, 88153.19, -38153.19, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN118', 155, 18, 'Union County', 'Kids Recreation Trust Fund FY23', 'Recreation', '2023-11-10', '2025-11-10 23:59:59+00', 65000.00, 486621.44, -421621.44, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN119', 155, 18, 'Union County', 'Local Arts Grants Program (LAP) FY24', 'Recreation', '2024-01-01', '2024-12-31 23:59:59+00', 3300.00, 0.00, 3300.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN120', 155, 18, 'Union County', 'Local Arts Grants Program (LAP) FY25', 'Recreation', '2025-01-01', '2025-12-31 23:59:59+00', 3700.00, 3700.00, 0.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW()),
('DEV-LIN121', 155, 18, 'US Congress', 'Community Project Funding FY24', 'Recreation', '2023-02-05', '2030-07-01 23:59:59+00', 1000000.00, 0.00, 1000000.00, 'applicationRequired', 0, 0, true, true, true, true, true, true, 0.00, 0.00, 0.00, 0.00, 0.00, false, false, true, NOW(), NOW());

-- Create budget entries for all awards (10 categories per award)
-- This replicates the afterCreate hook that creates budget entries for each award
INSERT INTO "award_budget_entries" (
  "award_id", "name", "award_amount", "award_expended", "award_balance",
  "match_amount", "match_expended", "enabled", "created_at", "updated_at"
)
SELECT
  a.id as award_id,
  category.name,
  0.00 as award_amount,
  0.00 as award_expended,
  0.00 as award_balance,
  0.00 as match_amount,
  0.00 as match_expended,
  true as enabled,
  NOW() as created_at,
  NOW() as updated_at
FROM "awards" a
CROSS JOIN (
  VALUES
    ('Personnel'),
    ('Fringe Benefits'),
    ('Travel'),
    ('Equipment'),
    ('Supplies'),
    ('Contractual'),
    ('Construction'),
    ('Other'),
    ('Total Direct Charges'),
    ('Indirect Charges')
) AS category(name)
WHERE a."client_id" = 155 AND a."string_id" LIKE 'DEV-LIN%';

-- Create default user roles for awards
-- This replicates the afterCreate hook that creates user roles for users with defaultRole
-- Note: This assumes there are users with default_role set for client 155
-- You may need to adjust the user IDs based on your actual user data
INSERT INTO "award_user_roles" (
  "award_id", "user_id", "role", "enabled", "created_at", "updated_at"
)
SELECT
  a.id as award_id,
  u.id as user_id,
  u."default_role" as role,
  true as enabled,
  NOW() as created_at,
  NOW() as updated_at
FROM "awards" a
CROSS JOIN "employees" u
WHERE a."client_id" = 155
  AND a."string_id" LIKE 'DEV-LIN%'
  AND u."client_creator_id" = 155
  AND u."default_role" IN ('primaryOrganizationalContact', 'financeContact', 'resolutionOwner', 'purchasing')
  AND u."enabled" = true;
